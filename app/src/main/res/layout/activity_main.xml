<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorThemeBackground"
    android:orientation="vertical"
    tools:context=".ui.activity.MainActivity"
    tools:ignore="UselessParent,UseCompoundDrawables,ContentDescription">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="0dp"
        android:gravity="center|start"
        android:paddingLeft="15dp"
        android:paddingTop="13dp"
        android:paddingRight="15dp"
        android:paddingBottom="5dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:singleLine="true"
            android:text="@string/app_name"
            android:textColor="@color/colorTextGray"
            android:textSize="25sp"
            android:textStyle="bold" />

        <androidx.constraintlayout.utils.widget.ImageFilterView
            style="?android:attr/selectableItemBackgroundBorderless"
            android:layout_width="27dp"
            android:layout_height="27dp"
            android:layout_marginEnd="5dp"
            android:alpha="0.85"
            android:src="@mipmap/ic_github"
            android:tint="@color/colorTextGray" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/main_lin_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="5dp"
        android:background="@drawable/bg_dark_round"
        android:elevation="0dp"
        android:gravity="center">

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/main_img_status"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginStart="25dp"
            android:layout_marginEnd="5dp"
            android:src="@mipmap/ic_warn"
            android:tint="@color/white" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="20dp"
            android:paddingTop="10dp"
            android:paddingRight="20dp"
            android:paddingBottom="10dp">

            <TextView
                android:id="@+id/main_text_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="@string/module_not_activated"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:gravity="center|start"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/main_text_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:alpha="0.8"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="@string/module_version"
                    android:textColor="@color/white"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/main_text_release_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:background="@drawable/bg_orange_round"
                    android:ellipsize="end"
                    android:paddingLeft="5dp"
                    android:paddingTop="2dp"
                    android:paddingRight="5dp"
                    android:paddingBottom="2dp"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textSize="11sp"
                    android:visibility="gone" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0.8"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="- Your custom text here -"
                android:textColor="@color/white"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/main_text_api_way"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:alpha="0.6"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="11sp"
                android:visibility="gone" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="15dp"
        android:background="@drawable/bg_permotion_round"
        android:elevation="0dp"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingLeft="15dp"
        android:paddingTop="15dp"
        android:paddingRight="15dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center|start">

            <ImageView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginEnd="10dp"
                android:src="@mipmap/ic_home" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:alpha="0.85"
                android:singleLine="true"
                android:text="@string/display_settings"
                android:textColor="@color/colorTextGray"
                android:textSize="12sp" />
        </LinearLayout>

        <me.zixue.sgkhook.ui.view.MaterialSwitch
            android:id="@+id/hide_icon_in_launcher_switch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/hide_app_icon_on_launcher"
            android:textAllCaps="false"
            android:textColor="@color/colorTextGray"
            android:textSize="15sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:alpha="0.6"
            android:lineSpacingExtra="6dp"
            android:text="@string/hide_app_icon_on_launcher_tip"
            android:textColor="@color/colorTextDark"
            android:textSize="12sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:alpha="0.6"
            android:lineSpacingExtra="6dp"
            android:text="@string/hide_app_icon_on_launcher_notice"
            android:textColor="#FF5722"
            android:textSize="12sp" />
    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:fadingEdgeLength="10dp"
        android:fillViewport="true"
        android:requiresFadingEdge="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/bg_permotion_round"
                android:gravity="center|start"
                android:orientation="horizontal"
                android:padding="10dp">

                <ImageView
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_marginEnd="10dp"
                    android:src="@mipmap/ic_yukihookapi" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:autoLink="web"
                    android:ellipsize="end"
                    android:lineSpacingExtra="6dp"
                    android:maxLines="2"
                    android:text="@string/about_module"
                    android:textColor="@color/colorTextGray"
                    android:textSize="11sp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>