<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:name=".application.DefaultApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.AppDefault"
        tools:targetApi="31">

        <meta-data
            android:name="xposedmodule"
            android:value="true" />
        <meta-data
            android:name="xposedscope"
            android:resource="@array/module_scope" />
        <meta-data
            android:name="xposeddescription"
            android:value="@string/xposed_desc" />
        <meta-data
            android:name="xposedminversion"
            android:value="93" />

        <activity
            android:name=".ui.activity.MainActivity"
            android:exported="true"
            android:screenOrientation="behind">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="de.robv.android.xposed.category.MODULE_SETTINGS" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name=".Home"
            android:exported="true"
            android:label="@string/app_name"
            android:screenOrientation="behind"
            android:targetActivity=".ui.activity.MainActivity">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
    </application>
</manifest>